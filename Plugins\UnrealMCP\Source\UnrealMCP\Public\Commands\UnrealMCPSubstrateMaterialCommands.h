#pragma once

#include "CoreMinimal.h"
#include "Json.h"

/**
 * Handles Substrate Material related MCP commands for UE 5.6
 * Substrate Materials replace the fixed suite of shading models with a more expressive framework
 * This class provides production-ready support for the new Substrate Material system
 */
class UNREALMCP_API FUnrealMCPSubstrateMaterialCommands
{
public:
    FUnrealMCPSubstrateMaterialCommands();

    /**
     * Handle Substrate Material-related commands
     * @param CommandType - The type of command to handle
     * @param Params - JSON parameters for the command
     * @return JSON response with results or error
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    /**
     * Create a new Substrate Material using UE 5.6 APIs
     * @param Params - Must include:
     *                "material_name" - Name for the new material
     *                "material_path" - Path where to create the material (optional)
     *                "substrate_type" - Type of substrate material (optional, default: "BSDF")
     * @return JSON response with the created material details
     */
    TSharedPtr<FJsonObject> HandleCreateSubstrateMaterial(const TSharedPtr<FJsonObject>& Params);

    /**
     * Configure Substrate Material properties using modern UE 5.6 APIs
     * @param Params - Must include:
     *                "material_name" - Name of the target material
     *                "properties" - JSON object with material properties
     * @return JSON response with configuration results
     */
    TSharedPtr<FJsonObject> HandleConfigureSubstrateMaterial(const TSharedPtr<FJsonObject>& Params);

    /**
     * Add Substrate nodes to a material graph
     * @param Params - Must include:
     *                "material_name" - Name of the target material
     *                "node_type" - Type of Substrate node to add
     *                "node_properties" - Properties for the new node
     * @return JSON response with node creation results
     */
    TSharedPtr<FJsonObject> HandleAddSubstrateNode(const TSharedPtr<FJsonObject>& Params);

    /**
     * Convert legacy material to Substrate Material
     * @param Params - Must include:
     *                "source_material" - Path to the legacy material
     *                "target_material" - Path for the new Substrate material
     * @return JSON response with conversion results
     */
    TSharedPtr<FJsonObject> HandleConvertToSubstrateMaterial(const TSharedPtr<FJsonObject>& Params);

    /**
     * Get Substrate Material information and capabilities
     * @param Params - Must include:
     *                "material_name" - Name of the material to inspect
     * @return JSON response with material information
     */
    TSharedPtr<FJsonObject> HandleGetSubstrateMaterialInfo(const TSharedPtr<FJsonObject>& Params);

    /**
     * Enable/Disable Substrate Materials system in the project
     * @param Params - Must include:
     *                "enabled" - Boolean to enable/disable Substrate
     * @return JSON response with system status
     */
    TSharedPtr<FJsonObject> HandleToggleSubstrateSystem(const TSharedPtr<FJsonObject>& Params);

private:
    // Helper functions for Substrate Material operations
    bool IsSubstrateEnabled() const;
    bool ValidateSubstrateMaterialName(const FString& MaterialName) const;
    FString GetDefaultSubstrateMaterialPath() const;

    /**
     * Validate required parameters in JSON object
     * @param Params - JSON object to validate
     * @param RequiredFields - Array of required field names
     * @return JSON error response if validation fails, nullptr if successful
     */
    TSharedPtr<FJsonObject> ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params, const TArray<FString>& RequiredFields);
};
